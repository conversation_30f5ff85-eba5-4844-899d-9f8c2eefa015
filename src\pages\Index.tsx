import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Shield, AlertTriangle, CheckCircle, Zap, Users, FileText, Settings, <PERSON><PERSON>hart, BarChart3, TrendingUp, Activity, Lock } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import WalletConnect from '@/components/WalletConnect';
import ThreatMonitor from '@/components/ThreatMonitor';
import TransactionHistory from '@/components/TransactionHistory';
import DAOPanel from '@/components/DAOPanel';
import TransactionInterceptor from '@/components/TransactionInterceptor';
import SecurityScore from '@/components/SecurityScore';
import AILearningFeedback from '@/components/AILearningFeedback';
import TelegramCompanion from '@/components/TelegramCompanion';
import TelegramSettings from '@/components/TelegramSettings';
import WalletAnalytics from '@/components/WalletAnalytics';

const Index = () => {
  const [walletConnected, setWalletConnected] = useState(false);
  const [currentAddress, setCurrentAddress] = useState('');
  const [threatLevel, setThreatLevel] = useState<'safe' | 'warning' | 'danger'>('safe');
  const [showInterceptor, setShowInterceptor] = useState(false);
  const [transactionDetails, setTransactionDetails] = useState({
    fromAddress: '',
    toAddress: '',
    value: 0,
    gasPrice: 0
  });
  const [suspiciousAddress, setSuspiciousAddress] = useState('******************************************');  // Add this line
  const [activeTab, setActiveTab] = useState('overview');
  const [aiScansToday, setAiScansToday] = useState(247);
  const [blockedThreats, setBlockedThreats] = useState(15);
  const [savedAmount, setSavedAmount] = useState(12450);
  
  // New gamification states
  const [securityScore, setSecurityScore] = useState(67);
  const [shieldLevel, setShieldLevel] = useState('Defender');
  const [showAIFeedback, setShowAIFeedback] = useState(false);
  const [lastAction, setLastAction] = useState<'vote' | 'report' | 'block' | 'scan'>('scan');
  const [isProcessing, setIsProcessing] = useState(false);
  
  const { toast } = useToast();

  // Reset threat level after some time for demo purposes
  useEffect(() => {
    if (threatLevel === 'danger' && !showInterceptor && !isProcessing) {
      const timer = setTimeout(() => {
        setThreatLevel('safe');
        toast({
          title: "System Secured",
          description: "Threat level returned to safe after blocking malicious transaction.",
        });
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [threatLevel, showInterceptor, isProcessing, toast]);

  const getThreatColor = (level: string) => {
    switch (level) {
      case 'safe': return 'text-green-500 bg-green-100';
      case 'warning': return 'text-yellow-500 bg-yellow-100';
      case 'danger': return 'text-red-500 bg-red-100';
      default: return 'text-gray-500 bg-gray-100';
    }
  };

  const simulateScamTransaction = () => {
    if (isProcessing) return;
    
    console.log('Simulating scam transaction...');
    setIsProcessing(true);
    
    // Set transaction details for the interceptor
    setTransactionDetails({
      fromAddress: currentAddress || '0x742d35Cc6634C0532925a3b8D4C9db96c4b4d8b',
      toAddress: '******************************************',
      value: 0.00000000000001,
      gasPrice: 20
    });

    setAiScansToday(prev => prev + 1);
    setThreatLevel('danger');
    setLastAction('scan');
    setShowAIFeedback(true);
    
    toast({
      title: "⚠️ Analyzing Transaction",
      description: "ML model is analyzing the transaction...",
      variant: "default",
    });

    setTimeout(() => {
      setShowInterceptor(true);
      setIsProcessing(false);
    }, 800);
  };

  const handleBlockTransaction = () => {
    console.log('Transaction blocked by user');
    
    setBlockedThreats(prev => prev + 1);
    setSavedAmount(prev => prev + Math.floor(Math.random() * 5000) + 1000);
    setSecurityScore(prev => Math.min(100, prev + 3));
    setLastAction('block');
    setShowAIFeedback(true);
    
    setShowInterceptor(false);
    setIsProcessing(false);
    
    toast({
      title: "🛡️ Transaction Blocked",
      description: "Malicious transaction successfully blocked. Your funds are safe!",
    });

    setTimeout(() => {
      setThreatLevel('safe');
    }, 2000);
  };

  const handleCloseInterceptor = () => {
    console.log('Interceptor closed');
    setShowInterceptor(false);
    setIsProcessing(false);
    
    toast({
      title: "⚠️ Transaction Signed",
      description: "You chose to proceed with the risky transaction.",
      variant: "destructive",
    });
    
    setTimeout(() => {
      setThreatLevel('warning');
    }, 1000);
  };

  const handleDAOVote = (proposalId: number, vote: 'approve' | 'reject') => {
    console.log(`Voting ${vote} on proposal ${proposalId}`);
    setSecurityScore(prev => Math.min(100, prev + 2));
    setLastAction('vote');
    setShowAIFeedback(true);
    
    toast({
      title: "🗳️ Vote Recorded",
      description: `Your ${vote} vote has been submitted to the DAO.`,
    });
  };

  const handleThreatReport = () => {
    setSecurityScore(prev => Math.min(100, prev + 5));
    setLastAction('report');
    setShowAIFeedback(true);
    
    toast({
      title: "📊 Report Submitted",
      description: "Thank you for helping secure the Web3 community!",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* New Header Design */}
      <header className="border-b border-white/10 bg-black/20 backdrop-blur-lg sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Left: Brand */}
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Shield className="h-8 w-8 text-cyan-400" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <h1 className="text-2xl font-bold text-white">The Unhackable Wallet</h1>
            </div>

            {/* Center: Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <button
                onClick={() => setActiveTab('overview')}
                className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                  activeTab === 'overview'
                    ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-500/30'
                    : 'text-gray-300 hover:text-white hover:bg-white/5'
                }`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                  activeTab === 'analytics'
                    ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-500/30'
                    : 'text-gray-300 hover:text-white hover:bg-white/5'
                }`}
              >
                Wallet Analysis
              </button>
              <button
                onClick={() => setActiveTab('dao')}
                className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                  activeTab === 'dao'
                    ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-500/30'
                    : 'text-gray-300 hover:text-white hover:bg-white/5'
                }`}
              >
                DAO Voting
              </button>
              <button
                onClick={() => setActiveTab('reports')}
                className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                  activeTab === 'reports'
                    ? 'bg-cyan-500/20 text-cyan-400 border border-cyan-500/30'
                    : 'text-gray-300 hover:text-white hover:bg-white/5'
                }`}
              >
                Thread Report
              </button>
            </nav>

            {/* Right: Wallet Connect & Settings */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setActiveTab('settings')}
                className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-white/5 transition-all duration-200"
                title="Settings"
              >
                <Settings className="h-5 w-5" />
              </button>
              <WalletConnect
                onConnect={(address) => {
                  setWalletConnected(true);
                  setCurrentAddress(address);
                  toast({
                    title: "Wallet Connected",
                    description: `Connected to ${address.slice(0, 6)}...${address.slice(-4)}`,
                  });
                }}
                isConnected={walletConnected}
                address={currentAddress}
              />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Hero Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center py-16">
              {/* Left Side - Text Content */}
              <div className="space-y-6">
                <div className="relative inline-block">
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
                  <Shield className="relative h-16 w-16 text-cyan-400" />
                </div>
                <h1 className="text-5xl lg:text-6xl font-bold text-white leading-tight">
                  Secure Your Web3 Journey
                </h1>
                <p className="text-xl text-gray-300 leading-relaxed">
                  AI-powered protection against scams, fraud, and malicious transactions.
                  Your ultimate guardian in the decentralized world.
                </p>
                {!walletConnected && (
                  <div className="pt-4">
                    <Button
                      onClick={() => {/* Trigger wallet connect */}}
                      className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white px-8 py-4 text-lg rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      Get Started - Connect Wallet
                    </Button>
                  </div>
                )}
              </div>

              {/* Right Side - Animated Wallet */}
              <div className="relative flex justify-center lg:justify-end">
                <div className="relative">
                  {/* Glowing background effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/30 to-blue-500/30 rounded-full blur-3xl animate-pulse"></div>

                  {/* Animated Wallet Container */}
                  <div className="relative w-80 h-80 flex items-center justify-center">
                    {/* Animated Wallet Icon */}
                    <div className="relative animate-bounce">
                      <div className="w-48 h-32 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500">
                        {/* Wallet Front */}
                        <div className="absolute inset-0 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-2xl p-6">
                          {/* Wallet Details */}
                          <div className="h-full flex flex-col justify-between">
                            {/* Top Section */}
                            <div className="flex justify-between items-start">
                              <div className="w-8 h-6 bg-white/20 rounded backdrop-blur-sm"></div>
                              <div className="text-white text-xs font-bold">SECURE</div>
                            </div>

                            {/* Middle Section - Card Number */}
                            <div className="space-y-1">
                              <div className="flex space-x-2">
                                <div className="w-8 h-2 bg-white/30 rounded"></div>
                                <div className="w-8 h-2 bg-white/30 rounded"></div>
                                <div className="w-8 h-2 bg-white/30 rounded"></div>
                                <div className="w-8 h-2 bg-white/30 rounded"></div>
                              </div>
                            </div>

                            {/* Bottom Section */}
                            <div className="flex justify-between items-end">
                              <div>
                                <div className="w-16 h-2 bg-white/20 rounded mb-1"></div>
                                <div className="w-12 h-2 bg-white/20 rounded"></div>
                              </div>
                              <Shield className="h-6 w-6 text-white/80" />
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Wallet Shadow/Back */}
                      <div className="absolute top-2 left-2 w-48 h-32 bg-gradient-to-br from-slate-600 to-slate-800 rounded-2xl -z-10 transform rotate-6"></div>
                    </div>

                    {/* Floating Security Elements */}
                    <div className="absolute top-8 right-8 animate-float">
                      <div className="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-green-500/30">
                        <CheckCircle className="h-6 w-6 text-green-400" />
                      </div>
                    </div>

                    <div className="absolute bottom-12 left-8 animate-float-delayed">
                      <div className="w-10 h-10 bg-cyan-500/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-cyan-500/30">
                        <Lock className="h-5 w-5 text-cyan-400" />
                      </div>
                    </div>

                    <div className="absolute top-16 left-12 animate-pulse">
                      <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                    </div>

                    <div className="absolute bottom-20 right-16 animate-pulse delay-500">
                      <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="bg-black/20 backdrop-blur-lg border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">AI Scans Today</p>
                      <p className="text-2xl font-bold text-white">{aiScansToday.toLocaleString()}</p>
                    </div>
                    <div className="p-3 bg-cyan-500/20 rounded-lg">
                      <Activity className="h-6 w-6 text-cyan-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-black/20 backdrop-blur-lg border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Threats Blocked</p>
                      <p className="text-2xl font-bold text-white">{blockedThreats}</p>
                    </div>
                    <div className="p-3 bg-red-500/20 rounded-lg">
                      <Shield className="h-6 w-6 text-red-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-black/20 backdrop-blur-lg border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Assets Protected</p>
                      <p className="text-2xl font-bold text-white">${savedAmount.toLocaleString()}</p>
                    </div>
                    <div className="p-3 bg-green-500/20 rounded-lg">
                      <TrendingUp className="h-6 w-6 text-green-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-black/20 backdrop-blur-lg border-white/10">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Security Score</p>
                      <p className="text-2xl font-bold text-white">{securityScore}/100</p>
                    </div>
                    <div className="p-3 bg-purple-500/20 rounded-lg">
                      <Lock className="h-6 w-6 text-purple-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Feature Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-black/20 backdrop-blur-lg border-white/10 hover:border-cyan-500/30 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="p-4 bg-cyan-500/20 rounded-full w-fit mx-auto mb-4">
                    <Shield className="h-8 w-8 text-cyan-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">Real-time Protection</h3>
                  <p className="text-gray-400">
                    AI-powered scanning of every transaction before it's executed,
                    protecting you from scams and malicious contracts.
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-black/20 backdrop-blur-lg border-white/10 hover:border-purple-500/30 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="p-4 bg-purple-500/20 rounded-full w-fit mx-auto mb-4">
                    <BarChart3 className="h-8 w-8 text-purple-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">Smart Analytics</h3>
                  <p className="text-gray-400">
                    Deep insights into your wallet activity, spending patterns,
                    and security recommendations tailored to your behavior.
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-black/20 backdrop-blur-lg border-white/10 hover:border-green-500/30 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="p-4 bg-green-500/20 rounded-full w-fit mx-auto mb-4">
                    <Users className="h-8 w-8 text-green-400" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">Community Driven</h3>
                  <p className="text-gray-400">
                    Participate in DAO governance, report threats, and help build
                    the most secure Web3 ecosystem together.
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <div className="bg-black/20 backdrop-blur-lg border border-white/10 rounded-xl p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Quick Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Button
                  onClick={() => setActiveTab('analytics')}
                  className="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 hover:from-cyan-500/30 hover:to-blue-500/30 text-white border border-cyan-500/30 h-auto p-4 flex flex-col items-center space-y-2"
                >
                  <PieChart className="h-6 w-6" />
                  <span>View Analytics</span>
                </Button>
                <Button
                  onClick={() => setActiveTab('dao')}
                  className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 hover:from-purple-500/30 hover:to-pink-500/30 text-white border border-purple-500/30 h-auto p-4 flex flex-col items-center space-y-2"
                >
                  <Users className="h-6 w-6" />
                  <span>DAO Voting</span>
                </Button>
                <Button
                  onClick={() => setActiveTab('reports')}
                  className="bg-gradient-to-r from-orange-500/20 to-red-500/20 hover:from-orange-500/30 hover:to-red-500/30 text-white border border-orange-500/30 h-auto p-4 flex flex-col items-center space-y-2"
                >
                  <FileText className="h-6 w-6" />
                  <span>Report Threat</span>
                </Button>
                <Button
                  onClick={() => setActiveTab('settings')}
                  className="bg-gradient-to-r from-gray-500/20 to-slate-500/20 hover:from-gray-500/30 hover:to-slate-500/30 text-white border border-gray-500/30 h-auto p-4 flex flex-col items-center space-y-2"
                >
                  <Settings className="h-6 w-6" />
                  <span>Settings</span>
                </Button>
              </div>
            </div>
          </div>
        )}



        {activeTab === 'analytics' && <WalletAnalytics walletAddress={currentAddress} />}

        {activeTab === 'dao' && (
            <div className="space-y-6">
              {/* Enhanced DAO Panel */}
              <Card className="bg-black/20 backdrop-blur-lg border-white/10">
                <CardHeader>
                  <CardTitle className="text-white">Community DAO Voting</CardTitle>
                  <p className="text-gray-400">
                    Vote on community threat reports and earn Shield Points! 
                    <span className="text-cyan-400">+2 points per vote</span>
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Sample proposals with enhanced voting */}
                    <div className="p-4 bg-white/5 rounded-lg border border-white/10">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h4 className="text-white font-medium">Malicious NFT Minting Contract</h4>
                          <p className="text-sm text-gray-400">0x1234...5678 - Reported for unauthorized minting</p>
                        </div>
                        <Badge className="bg-red-500/20 text-red-400">High Risk</Badge>
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          size="sm" 
                          className="bg-green-600 hover:bg-green-700"
                          onClick={() => handleDAOVote(1, 'approve')}
                        >
                          Approve Block
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                          onClick={() => handleDAOVote(1, 'reject')}
                        >
                          Reject
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
        )}

        {activeTab === 'reports' && (
            <Card className="bg-black/20 backdrop-blur-lg border-white/10">
              <CardHeader>
                <CardTitle className="text-white">Community Threat Reports</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-400">
                    Help protect the Web3 community by reporting suspicious contracts and activities.
                    <span className="text-purple-400 font-medium"> Earn +5 Shield Points per verified report!</span>
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Recent reports with enhanced styling */}
                    <div className="p-4 bg-white/5 rounded-lg border border-white/10">
                      <h4 className="text-white font-medium mb-2">Recent Reports</h4>
                      <div className="text-sm text-gray-400">
                        <div className="flex justify-between items-center mb-2">
                          <span>Token Drainer</span>
                          <Badge className="bg-red-500/20 text-red-400">High Risk</Badge>
                        </div>
                        <div className="flex justify-between items-center mb-2">
                          <span>Fake Airdrop</span>
                          <Badge className="bg-yellow-500/20 text-yellow-400">Medium Risk</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>Rug Pull Contract</span>
                          <Badge className="bg-green-500/20 text-green-400">Resolved</Badge>
                        </div>
                      </div>
                    </div>
                    {/* Enhanced submit button */}
                    <div className="p-4 bg-white/5 rounded-lg border border-white/10">
                      <h4 className="text-white font-medium mb-2">Submit New Report</h4>
                      <Button 
                        className="w-full bg-cyan-600 hover:bg-cyan-700"
                        onClick={handleThreatReport}
                      >
                        Report Suspicious Activity (+5 Points)
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
        )}

        {activeTab === 'settings' && (
            <div className="space-y-6">
              <Card className="bg-black/20 backdrop-blur-lg border-white/10">
                <CardHeader>
                  <CardTitle className="text-white">Security Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                      <div>
                        <h4 className="text-white font-medium">Real-time Protection</h4>
                        <p className="text-sm text-gray-400">Enable AI-powered transaction scanning</p>
                      </div>
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                      <div>
                        <h4 className="text-white font-medium">Auto-block High Risk</h4>
                        <p className="text-sm text-gray-400">Automatically block transactions with 90%+ risk score</p>
                      </div>
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                      <div>
                        <h4 className="text-white font-medium">Community Reports</h4>
                        <p className="text-sm text-gray-400">Show warnings from community-reported contracts</p>
                      </div>
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Telegram Settings Integration */}
              <TelegramSettings walletAddress={currentAddress} />
            </div>
        )}
      </main>

      {/* Enhanced Modals and Notifications */}
      {showInterceptor && (
        <TransactionInterceptor 
          onClose={handleCloseInterceptor}
          onBlock={handleBlockTransaction}
          fromAddress={transactionDetails.fromAddress}
          toAddress={transactionDetails.toAddress}
          value={transactionDetails.value}
          gasPrice={transactionDetails.gasPrice}
        />
      )}

      {/* AI Learning Feedback */}
      <AILearningFeedback 
        trigger={showAIFeedback}
        actionType={lastAction}
        onComplete={() => setShowAIFeedback(false)}
      />

      {/* Telegram Companion */}
      <TelegramCompanion />
    </div>
  );
};

export default Index;
